写代码的时候，遵循下面的逻辑：
  . 在写代码之前，充分梳理这次需求所涉及的上下文
  . 设计代码逻辑的时候，单个文件不要超过200行，如果超过了，就封装到单独的文件中
  . 设计代码逻辑的时候，必要的时候进行适当的封装函数，确保代码逻辑清晰易读
  . 中文回答，写完后尝试编译
  . 除了我当前提到的业务和相关逻辑，不要修改任何其他不相关的代码
  . 写完代码尝试编译测试环境，确保编译通过。编译的方式是用/Users/<USER>/fang/flutter/bin/flutter build apk --debug，记得是打测试包不是生产包
  . 写代码的过程中，自行判断是否要新增测试文件，如果新增了测试文件，测试完成之后要删除
  . 写完代码之后，检查一下在这次实现的过程中新增的代码，有没有哪些是可以删除的，不要增加无用的代码

我的需求是：
当前开启权限之后，如果都有权限了，就要主动开启侧边栏